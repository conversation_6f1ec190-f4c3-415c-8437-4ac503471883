import { defineStore } from 'pinia';
import type { ItemBlock } from 'src/types/models';
import { ref, computed } from 'vue';

// BlockData interface for UI compatibility
// export interface BlockData {
//   id: number;
//   type: 'header' | 'item' | 'image';
//   questionText?: string;
//   selectedType?: string;
//   isSectionStart?: boolean;
//   imageData?: string;
//   isRequired?: boolean;
// }

type DOMRefElement = Element | null;

// Allowed types for AnswerItemBlockType
const allowedTypes: string[] = ['RADIO', 'CHECKBOX', 'TEXTFIELD', 'GRID'];

export const useBlockCreatorStore = defineStore('blockCreator', () => {
  // State
  const blocks = ref<ItemBlock[]>([]);
  const blockRefs: Record<number, DOMRefElement> = {};
  const selectedBlock = ref<ItemBlock | null>(null);
  const selectedBlockId = ref<string>();

  // let nextId = 1;

  // ID generation utilities
  const lastUsedQuestionId = ref(0);
  const lastUsedOptionId = ref(0);

  function generateQuestionId(): number {
    lastUsedQuestionId.value++;
    return lastUsedQuestionId.value;
  }
  function generateOptionId(): number {
    lastUsedOptionId.value++;
    return lastUsedOptionId.value;
  }

  // Block operations
  function addBlock(item: ItemBlock, index: number) {
    blocks.value.splice(index + 1, 0, item);
  }

  function appendBlock(item: ItemBlock) {
    blocks.value.push(item);
  }

  function updateBlocksOrder(newBlocks: ItemBlock[]) {
    // Update the blocks array with the new order
    blocks.value = newBlocks;

    // Update sequence numbers to match the new order
    blocks.value.forEach((block, index) => {
      block.sequence = index + 1;
    });
  }

  function setSection(value: number, index: number) {
    blocks.value[index] = {
      ...blocks.value[index],
      section: value,
    } as ItemBlock;
  }

  function updateBlock(item: ItemBlock, index: number) {
    blocks.value[index] = item;
  }

  function deleteBlock(index: number) {
    blocks.value.splice(index, 1);
  }

  function duplicateBlock(source: ItemBlock, index: number): ItemBlock {
    // Generate new unique IDs for the duplicated block
    const newBlockId = generateItemBlockId();
    const newSequence = index + 2; // Place after the current block

    // Deep clone the source block with new IDs
    const newBlock: ItemBlock = {
      ...source,
      id: newBlockId,
      sequence: newSequence,
      // Clone headerBody if exists
      headerBody: source.headerBody
        ? {
            ...source.headerBody,
            id: generateHeaderBodyId(),
            itemBlockId: newBlockId,
          }
        : undefined,
      // Clone imageBody if exists
      imageBody: source.imageBody
        ? {
            ...source.imageBody,
            id: generateImageBodyId(),
            itemBlockId: newBlockId,
          }
        : undefined,
      // Clone questions if exist
      questions: source.questions
        ? source.questions.map((question) => ({
            ...question,
            id: generateQuestionId(),
            itemBlockId: newBlockId,
          }))
        : undefined,
      // Clone options if exist
      // Note: For frontend-only duplicates, we use negative IDs to indicate they're temporary
      // This prevents auto-save from trying to update non-existent backend entities
      options: source.options
        ? source.options.map((option) => ({
            ...option,
            id: -Math.abs(generateOptionId()), // Negative ID indicates frontend-only
            itemBlockId: newBlockId,
          }))
        : undefined,
    };

    // Insert the duplicated block after the source block
    blocks.value.splice(index + 1, 0, newBlock);

    // Update sequences for all blocks after the insertion point
    updateSequencesAfterInsertion(index + 1);

    return newBlock;
  }

  function updateSequencesAfterInsertion(startIndex: number) {
    for (let i = startIndex; i < blocks.value.length; i++) {
      blocks.value[i].sequence = i + 1;
    }
  }

  // Section helpers
  function isSectionBlock(index: number): boolean {
    const current = blocks.value[index];
    if (!current) return false;

    // A block is considered a section start if:
    // 1. It's a HEADER block, AND
    // 2. It's either the first block OR has a different section number than the previous block
    if (current.type === 'HEADER') {
      if (index === 0) return true; // First block is always a section start

      const previous = blocks.value[index - 1];
      if (!previous) return true;

      // Check if this header starts a new section
      return current.section !== previous.section;
    }

    return false;
  }

  const totalSections = computed(() => {
    // Count unique section numbers from all blocks
    const sectionNumbers = new Set(blocks.value.map((block) => block.section));
    return sectionNumbers.size;
  });

  function getSectionNumber(index: number): number {
    const current = blocks.value[index];
    if (!current) return 1;

    // Return the section number of the current block
    return current.section || 1;
  }

  // Block refs for scrolling/focus
  function setBlockRef(id: number, el: DOMRefElement) {
    blockRefs[id] = el;
  }
  function getBlockRef(id: number) {
    return blockRefs[id];
  }

  // Initialization helpers
  function resetBlocks(initialBlocks: ItemBlock[]) {
    blocks.value = initialBlocks;
  }

  function initializeBlocks(initialBlocks: ItemBlock[]) {
    blocks.value = [...initialBlocks];
  }

  function isAnswerItemBlockType(type: string): boolean {
    return allowedTypes.includes(type);
  }

  // Assessment data conversion
  function getAssessmentData() {
    return {
      blocks: blocks.value,
      totalBlocks: blocks.value.length,
      totalSections: totalSections.value,
    };
  }

  // Expose API
  return {
    isAnswerItemBlockType,
    blocks,
    addBlock,
    appendBlock,
    updateBlocksOrder,
    setSection,
    updateBlock,
    deleteBlock,
    duplicateBlock,
    isSectionBlock,
    totalSections,
    getSectionNumber,
    setBlockRef,
    getBlockRef,
    resetBlocks,
    initializeBlocks,
    generateQuestionId,
    generateOptionId,
    selectedBlock,
    selectedBlockId,
    getAssessmentData,
  };
});
